import {
  Button,
  Form,
  FormItem,
  Header,
  Input,
  Options,
  RouteLink,
  Select,
  Typography,
} from '@/components/core'
import { paths } from '@/constants'
import { useCompanyId } from '@/hooks'
import { api } from '@/api'
import { useQuery } from '@tanstack/react-query'
import { type FC } from 'react'
import { useForm } from 'react-hook-form'
import { useNavigate, useParams, useSearchParams } from 'react-router'

export const CreateProduct: FC = () => {
  const companyId = useCompanyId()

  if (companyId === null) {
    return <Typography>Please, select a company</Typography>
  }

  return (
    <ProductForm
      action="Create"
      title="Create a new product"
      values={{ name: '', base_unit_id: '' }}
      onSubmit={async values => {
        await api.createProduct({ body: { ...values, company_id: companyId } })
      }}
    />
  )
}

export const EditProduct: FC = () => {
  const { id } = useParams()

  const getProduct = useQuery({
    queryKey: ['product', id],
    queryFn: () => api.getProduct({ params: { id: id! } }),
    enabled: id !== undefined,
  })

  if (id === undefined || getProduct.data === undefined) return null

  return (
    <ProductForm
      action="Save"
      title="Edit a product"
      values={getProduct.data}
      onSubmit={async values => {
        await api.updateProduct({ params: { id }, body: values })
      }}
    />
  )
}

type Product = { name: string; base_unit_id: string }
type ProductFormProps = {
  action: string
  title: string
  values: Product
  onSubmit: (values: Product) => Promise<unknown>
}
const ProductForm: FC<ProductFormProps> = ({
  action,
  title,
  values,
  onSubmit,
}) => {
  const [searchParams] = useSearchParams()
  const navigate = useNavigate()
  const form = useForm({ values })
  const companyId = useCompanyId()
  const getUnits = useQuery({
    queryKey: ['units', companyId],
    queryFn: () =>
      companyId ? api.getUnits({ query: { company_id: companyId } }) : null,
    enabled: !!companyId,
  })

  if (companyId === null) {
    return <Typography>Please, select a company</Typography>
  }

  return (
    <div>
      <nav>
        <RouteLink to={paths.products} end>
          List
        </RouteLink>
      </nav>
      <Header>{title}</Header>
      <Form
        onSubmit={form.handleSubmit(async values => {
          await onSubmit(values)
          await navigate({
            pathname: paths.products,
            search: searchParams.toString(),
          })
        })}
      >
        <FormItem text="Name">
          <Input type="text" autoComplete="off" {...form.register('name')} />
        </FormItem>
        <FormItem text="Base unit">
          <Select {...form.register('base_unit_id')}>
            <Options items={getUnits.data || []} />
          </Select>
        </FormItem>
        <Button type="submit" variant="primary">
          {action}
        </Button>
      </Form>
    </div>
  )
}
