import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Typography } from '@/components/core'
import { useCompanyId } from '@/hooks'
import { api } from '@/api'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { type FC, useState } from 'react'
import { useParams } from 'react-router'

type ProductUnit = {
  id: string
  created_at: string
  product_id: string
  unit_id: string
  is_base_unit: boolean
  unit_name: string
}

type Unit = { id: string; name: string }

export const ProductUnits: FC = () => {
  const { id: productId } = useParams()
  const companyId = useCompanyId()
  const queryClient = useQueryClient()
  const [selectedUnitId, setSelectedUnitId] = useState('')

  const getProduct = useQuery({
    queryKey: ['product', productId],
    queryFn: () => api.getProduct({ params: { id: productId! } }),
    enabled: !!productId,
  })

  const getProductUnits = useQuery({
    queryKey: ['product-units', productId],
    queryFn: () => api.getProductUnits({ params: { id: productId! } }),
    enabled: !!productId,
  })

  const getUnits = useQuery({
    queryKey: ['units', companyId],
    queryFn: () =>
      companyId ? api.getUnits({ query: { company_id: companyId } }) : null,
    enabled: !!companyId,
  })

  const addProductUnit = useMutation({
    mutationFn: (unitId: string) =>
      api.addProductUnit({
        params: { id: productId! },
        body: { unit_id: unitId },
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['product-units', productId] })
      setSelectedUnitId('')
    },
  })

  const removeProductUnit = useMutation({
    mutationFn: (unitId: string) =>
      api.removeProductUnit({ params: { id: productId!, unit_id: unitId } }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['product-units', productId] })
    },
  })

  const setBaseUnit = useMutation({
    mutationFn: (unitId: string) =>
      api.setProductBaseUnit({
        params: { id: productId! },
        body: { unit_id: unitId },
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['product-units', productId] })
      queryClient.invalidateQueries({ queryKey: ['products'] })
    },
  })

  if (!productId || !companyId) {
    return <Typography>Invalid product or company</Typography>
  }

  const productUnits = getProductUnits.data || []
  const allUnits = getUnits.data || []
  const availableUnits = allUnits.filter(
    (unit: Unit) =>
      !productUnits.some((pu: ProductUnit) => pu.unit_id === unit.id),
  )

  return (
    <div>
      <Header>
        Product Units
        {getProduct.data && ` - ${getProduct.data.name}`}
      </Header>

      <div style={{ marginBottom: '20px' }}>
        <h3>Current Units</h3>
        {productUnits.length === 0 ? (
          <Typography>No units assigned to this product</Typography>
        ) : (
          <ul>
            {productUnits.map((productUnit: ProductUnit) => (
              <li key={productUnit.id} style={{ marginBottom: '10px' }}>
                <span>
                  {productUnit.unit_name}
                  {productUnit.is_base_unit && ' (Base Unit)'}
                </span>
                <div style={{ marginLeft: '10px', display: 'inline-block' }}>
                  {!productUnit.is_base_unit && (
                    <Button
                      type="button"
                      variant="default"
                      onClick={() => setBaseUnit.mutate(productUnit.unit_id)}
                      disabled={setBaseUnit.isPending}
                      style={{ marginRight: '5px' }}
                    >
                      Set as Base
                    </Button>
                  )}
                  <Button
                    type="button"
                    variant="default"
                    onClick={() =>
                      removeProductUnit.mutate(productUnit.unit_id)
                    }
                    disabled={removeProductUnit.isPending}
                  >
                    Remove
                  </Button>
                </div>
              </li>
            ))}
          </ul>
        )}
      </div>

      <div>
        <h3>Add Unit</h3>
        {availableUnits.length === 0 ? (
          <Typography>All available units are already assigned</Typography>
        ) : (
          <div style={{ display: 'flex', gap: '10px', alignItems: 'center' }}>
            <Select
              value={selectedUnitId}
              onChange={e => setSelectedUnitId(e.target.value)}
            >
              <option value="">Select a unit...</option>
              {availableUnits.map(unit => (
                <option key={unit.id} value={unit.id}>
                  {unit.name}
                </option>
              ))}
            </Select>
            <Button
              type="button"
              variant="primary"
              onClick={() => {
                if (selectedUnitId) {
                  addProductUnit.mutate(selectedUnitId)
                }
              }}
              disabled={!selectedUnitId || addProductUnit.isPending}
            >
              Add Unit
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}
