import { But<PERSON>, Header, Input, Select, Typography } from '@/components/core'
import { useCompanyId } from '@/hooks'
import { api } from '@/api'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { type FC, useState } from 'react'
import { useParams } from 'react-router'

type ProductUnit = {
  id: string | null
  created_at: string
  product_id: string | null
  unit_id: string | null
  is_base_unit: boolean | null
  item_count: string | null
  unit_name: string
}

type Unit = { id: string; name: string }

export const ProductUnits: FC = () => {
  const { id: productId } = useParams()
  const companyId = useCompanyId()
  const queryClient = useQueryClient()
  const [selectedUnitId, setSelectedUnitId] = useState('')
  const [selectedItemCount, setSelectedItemCount] = useState('1')
  const [editingItemCount, setEditingItemCount] = useState<string | null>(null)
  const [editItemCountValue, setEditItemCountValue] = useState('')

  const getProduct = useQuery({
    queryKey: ['product', productId],
    queryFn: () => api.getProduct({ params: { id: productId! } }),
    enabled: !!productId,
  })

  const getProductUnits = useQuery({
    queryKey: ['product-units', productId],
    queryFn: () => api.getProductUnits({ params: { id: productId! } }),
    enabled: !!productId,
  })

  const getUnits = useQuery({
    queryKey: ['units', companyId],
    queryFn: () =>
      companyId ? api.getUnits({ query: { company_id: companyId } }) : null,
    enabled: !!companyId,
  })

  const addProductUnit = useMutation({
    mutationFn: ({
      unitId,
      itemCount,
    }: {
      unitId: string
      itemCount: number
    }) =>
      api.addProductUnit({
        params: { id: productId! },
        body: { unit_id: unitId, item_count: itemCount },
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['product-units', productId] })
      setSelectedUnitId('')
      setSelectedItemCount('1')
    },
  })

  const removeProductUnit = useMutation({
    mutationFn: (unitId: string) =>
      api.removeProductUnit({ params: { id: productId!, unit_id: unitId } }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['product-units', productId] })
    },
  })

  const setBaseUnit = useMutation({
    mutationFn: (unitId: string) =>
      api.setProductBaseUnit({
        params: { id: productId! },
        body: { unit_id: unitId },
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['product-units', productId] })
      queryClient.invalidateQueries({ queryKey: ['products'] })
    },
  })

  // TODO: Add updateItemCount mutation when API types are available
  // const updateItemCount = useMutation({
  //   mutationFn: ({ unitId, itemCount }: { unitId: string; itemCount: number }) =>
  //     api.updateProductUnitCount({
  //       params: { id: productId!, unit_id: unitId },
  //       body: { item_count: itemCount },
  //     }),
  //   onSuccess: () => {
  //     queryClient.invalidateQueries({ queryKey: ['product-units', productId] })
  //     setEditingItemCount(null)
  //   },
  // })

  if (!productId || !companyId) {
    return <Typography>Invalid product or company</Typography>
  }

  const productUnits = getProductUnits.data || []
  const allUnits = getUnits.data || []
  const availableUnits = allUnits.filter(
    (unit: Unit) =>
      !productUnits.some((pu: ProductUnit) => pu.unit_id === unit.id),
  )

  return (
    <div>
      <Header>
        Product Units
        {getProduct.data && ` - ${getProduct.data.name}`}
      </Header>

      <div style={{ marginBottom: '20px' }}>
        <h3>Current Units</h3>
        {productUnits.length === 0 ? (
          <Typography>No units assigned to this product</Typography>
        ) : (
          <ul>
            {productUnits.map((productUnit: ProductUnit) => (
              <li
                key={productUnit.id || 'unknown'}
                style={{
                  marginBottom: '15px',
                  padding: '10px',
                  border: '1px solid #ddd',
                  borderRadius: '4px',
                }}
              >
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                  }}
                >
                  <div>
                    <strong>{productUnit.unit_name}</strong>
                    {productUnit.is_base_unit && (
                      <span style={{ color: 'green', marginLeft: '8px' }}>
                        (Base Unit)
                      </span>
                    )}
                  </div>
                  <div
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '10px',
                    }}
                  >
                    <div
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '5px',
                      }}
                    >
                      <span>Count:</span>
                      {editingItemCount === productUnit.unit_id ? (
                        <div
                          style={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: '5px',
                          }}
                        >
                          <Input
                            type="number"
                            value={editItemCountValue}
                            onChange={e =>
                              setEditItemCountValue(e.target.value)
                            }
                            style={{ width: '80px' }}
                            step="0.1"
                            min="0.1"
                          />
                          <Button
                            type="button"
                            variant="primary"
                            onClick={() => {
                              // TODO: Implement when updateItemCount is available
                              alert('Item count editing will be available soon')
                            }}
                            disabled={false}
                          >
                            Save
                          </Button>
                          <Button
                            type="button"
                            variant="default"
                            onClick={() => setEditingItemCount(null)}
                          >
                            Cancel
                          </Button>
                        </div>
                      ) : (
                        <div
                          style={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: '5px',
                          }}
                        >
                          <span>{productUnit.item_count}</span>
                          {!productUnit.is_base_unit && (
                            <Button
                              type="button"
                              variant="link"
                              onClick={() => {
                                setEditingItemCount(productUnit.unit_id)
                                setEditItemCountValue(
                                  productUnit.item_count || '1',
                                )
                              }}
                            >
                              Edit
                            </Button>
                          )}
                        </div>
                      )}
                    </div>
                    <div style={{ display: 'flex', gap: '5px' }}>
                      {!productUnit.is_base_unit && (
                        <Button
                          type="button"
                          variant="default"
                          onClick={() => {
                            if (productUnit.unit_id) {
                              setBaseUnit.mutate(productUnit.unit_id)
                            }
                          }}
                          disabled={setBaseUnit.isPending}
                        >
                          Set as Base
                        </Button>
                      )}
                      <Button
                        type="button"
                        variant="default"
                        onClick={() => {
                          if (productUnit.unit_id) {
                            removeProductUnit.mutate(productUnit.unit_id)
                          }
                        }}
                        disabled={
                          removeProductUnit.isPending ||
                          Boolean(productUnit.is_base_unit)
                        }
                        title={
                          productUnit.is_base_unit
                            ? 'Cannot remove base unit'
                            : 'Remove unit'
                        }
                      >
                        Remove
                      </Button>
                    </div>
                  </div>
                </div>
              </li>
            ))}
          </ul>
        )}
      </div>

      <div>
        <h3>Add Unit</h3>
        {availableUnits.length === 0 ? (
          <Typography>All available units are already assigned</Typography>
        ) : (
          <div
            style={{
              display: 'flex',
              gap: '10px',
              alignItems: 'center',
              flexWrap: 'wrap',
            }}
          >
            <Select
              value={selectedUnitId}
              onChange={e => setSelectedUnitId(e.target.value)}
            >
              <option value="">Select a unit...</option>
              {availableUnits.map(unit => (
                <option key={unit.id} value={unit.id}>
                  {unit.name}
                </option>
              ))}
            </Select>
            <div style={{ display: 'flex', alignItems: 'center', gap: '5px' }}>
              <label>Count:</label>
              <Input
                type="number"
                value={selectedItemCount}
                onChange={e => setSelectedItemCount(e.target.value)}
                style={{ width: '80px' }}
                step="0.1"
                min="0.1"
                placeholder="1.0"
              />
            </div>
            <Button
              type="button"
              variant="primary"
              onClick={() => {
                if (selectedUnitId) {
                  const itemCount = parseFloat(selectedItemCount) || 1.0
                  addProductUnit.mutate({ unitId: selectedUnitId, itemCount })
                }
              }}
              disabled={!selectedUnitId || addProductUnit.isPending}
            >
              Add Unit
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}
