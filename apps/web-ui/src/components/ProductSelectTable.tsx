import { useCompanyId } from '@/hooks'
import { api } from '@/api'
import { useQuery } from '@tanstack/react-query'
import { type FC } from 'react'
import { ListTable, type Column } from './ListTable'

type Product = {
  base_unit_id: string
  base_unit_name: string
  company_id: string
  created_at: string
  id: string
  name: string
}

const columns: Column<Product>[] = [{ title: 'Name', cell: row => row.name }]

type ProductSelectTableProps = { onSelect: (product: Product) => void }
export const ProductSelectTable: FC<ProductSelectTableProps> = ({
  onSelect,
}) => {
  const companyId = useCompanyId()

  const getProducts = useQuery({
    queryKey: ['products', companyId],
    queryFn: () => api.getProducts({ query: { company_id: companyId! } }),
    enabled: companyId !== null,
  })

  if (getProducts.data === undefined) return null

  return (
    <ListTable
      rows={getProducts.data}
      columns={columns}
      onRowSelect={onSelect}
    />
  )
}
