import findMyWay from 'find-my-way'
import { registerRoute } from './http.js'
import { activateUser } from './routers/activateUser.js'
import { createCompany } from './routers/companies/createCompany.js'
import { deleteCompany } from './routers/companies/deleteCompany.js'
import { getCompanies } from './routers/companies/getCompanies.js'
import { getCompany } from './routers/companies/getCompany.js'
import { updateCompany } from './routers/companies/updateCompany.js'
import { createCustomer } from './routers/customers/createCustomer.js'
import { deleteCustomer } from './routers/customers/deleteCustomer.js'
import { getCustomer } from './routers/customers/getCustomer.js'
import { getCustomers } from './routers/customers/getCustomers.js'
import { updateCustomer } from './routers/customers/updateCustomer.js'
import { createOrder } from './routers/orders/createOrder.js'
import { deleteOrder } from './routers/orders/deleteOrder.js'
import { getOrder } from './routers/orders/getOrder.js'
import { getOrders } from './routers/orders/getOrders.js'
import { updateOrder } from './routers/orders/updateOrder.js'
import { addProductUnit } from './routers/products/addProductUnit.js'
import { createProduct } from './routers/products/createProduct.js'
import { getProduct } from './routers/products/getProduct.js'
import { getProductUnits } from './routers/products/getProductUnits.js'
import { getProducts } from './routers/products/getProducts.js'
import { removeProductUnit } from './routers/products/removeProductUnit.js'
import { setProductBaseUnit } from './routers/products/setProductBaseUnit.js'
import { updateProduct } from './routers/products/updateProduct.js'
import { createToken } from './routers/tokens/createToken.js'
import { deleteToken } from './routers/tokens/deleteToken.js'
import { getToken } from './routers/tokens/getToken.js'
import { authorizeUser } from './routers/users/authorizeUser.js'
import { getAuthorizedUsers } from './routers/users/getAuthorizedUsers.js'
import { registerUser } from './routers/users/registerUser.js'
import { createUnit } from './routers/units/createUnit.js'
import { getUnit } from './routers/units/getUnit.js'
import { getUnits } from './routers/units/getUnits.js'
import { updateUnit } from './routers/units/updateUnit.js'

export const app = findMyWay()
registerRoute(app, activateUser)
registerRoute(app, addProductUnit)
registerRoute(app, authorizeUser)
registerRoute(app, createCompany)
registerRoute(app, createCustomer)
registerRoute(app, createOrder)
registerRoute(app, createProduct)
registerRoute(app, createToken)
registerRoute(app, createUnit)
registerRoute(app, deleteCompany)
registerRoute(app, deleteCustomer)
registerRoute(app, deleteOrder)
registerRoute(app, deleteToken)
registerRoute(app, getAuthorizedUsers)
registerRoute(app, getCompanies)
registerRoute(app, getCompany)
registerRoute(app, getCustomer)
registerRoute(app, getCustomers)
registerRoute(app, getOrder)
registerRoute(app, getOrders)
registerRoute(app, getProduct)
registerRoute(app, getProductUnits)
registerRoute(app, getProducts)
registerRoute(app, getToken)
registerRoute(app, getUnit)
registerRoute(app, getUnits)
registerRoute(app, registerUser)
registerRoute(app, removeProductUnit)
registerRoute(app, setProductBaseUnit)
registerRoute(app, updateCompany)
registerRoute(app, updateCustomer)
registerRoute(app, updateOrder)
registerRoute(app, updateProduct)
registerRoute(app, updateUnit)
