import { createEndpoint } from './http-utils.js'
export const endpoints = {
  getUsersAuthorized: createEndpoint('GET', '/users/authorized'),
  registerUser: createEndpoint('POST', '/users/register'),
  updateUserAuthorize: createEndpoint('PUT', '/users/authorize'),
  getToken: createEndpoint<{ id: string }>('GET', '/tokens/:id'),
  createToken: createEndpoint('POST', '/tokens'),
  deleteToken: createEndpoint<{ id: string }>('DELETE', '/tokens/:id'),
  createUnit: createEndpoint('POST', '/units'),
  activateUser: createEndpoint<{ id: string }>('GET', '/activate/:id'),
  getCompanies: createEndpoint('GET', '/companies'),
  getCompany: createEndpoint<{ id: string }>('GET', '/companies/:id'),
  createCompany: createEndpoint('POST', '/companies'),
  updateCompany: createEndpoint<{ id: string }>('PUT', '/companies/:id'),
  deleteCompany: createEndpoint('DELETE', '/companies'),
  getCustomers: createEndpoint('GET', '/customers'),
  getCustomer: createEndpoint<{ id: string }>('GET', '/customers/:id'),
  createCustomer: createEndpoint('POST', '/customers'),
  updateCustomer: createEndpoint<{ id: string }>('PUT', '/customers/:id'),
  deleteCustomer: createEndpoint<{ id: string }>('DELETE', '/customers/:id'),
  getOrders: createEndpoint('GET', '/orders'),
  getOrder: createEndpoint<{ id: string }>('GET', '/orders/:id'),
  createOrder: createEndpoint('POST', '/orders'),
  updateOrder: createEndpoint<{ id: string }>('PUT', '/orders/:id'),
  deleteOrder: createEndpoint<{ id: string }>('DELETE', '/orders/:id'),
  getProducts: createEndpoint('GET', '/products'),
  getProduct: createEndpoint<{ id: string }>('GET', '/products/:id'),
  createProduct: createEndpoint('POST', '/products'),
  updateProduct: createEndpoint<{ id: string }>('PUT', '/products/:id'),
  getUnits: createEndpoint('GET', '/units'),
  getUnit: createEndpoint<{ id: string }>('GET', '/units/:id'),
  updateUnit: createEndpoint<{ id: string }>('PUT', '/units/:id'),
}
