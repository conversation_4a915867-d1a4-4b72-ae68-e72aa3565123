import { productDao, productUnitDao } from '../../daos/index.js'
import { endpoints } from '../../endpoints.js'
import { createHttpResult, createRoute } from '../../http.js'
import { type Fetcher } from '../../http-types.js'
import { pool } from '../../pool.js'
import { assertAccess, parseAuth } from '../../utils/index.js'

export const removeProductUnit = createRoute({
  endpoint: endpoints.removeProductUnit,
  handler: async ({ request, params }) => {
    const { user } = await parseAuth(request)
    const product = await productDao.selectOneById(pool, { id: params.id })
    await assertAccess(product.company_id, user.id, pool)
    
    const deletedProductUnit = await productUnitDao.deleteOne(pool, {
      product_id: params.id,
      unit_id: params.unit_id,
    })
    
    return createHttpResult(200, deletedProductUnit)
  },
})

export type RemoveProductUnitFetcher = Fetcher<typeof removeProductUnit>
