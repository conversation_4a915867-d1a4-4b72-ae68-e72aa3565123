import { z } from 'zod'
import { productDao } from '../../daos/index.js'
import { endpoints } from '../../endpoints.js'
import { createHttpResult, createRoute } from '../../http.js'
import { type Fetcher } from '../../http-types.js'
import { pool } from '../../pool.js'
import { assertAccess, parseAuth } from '../../utils/index.js'

export const updateProduct = createRoute({
  endpoint: endpoints.updateProduct,
  bodySchema: z.object({ name: z.string(), base_unit_id: z.string().uuid() }),
  handler: async ({ request, body, params }) => {
    const { user } = await parseAuth(request)
    const product = await productDao.selectOneById(pool, { id: params.id })
    await assertAccess(product.company_id, user.id, pool)
    const updatedProduct = await productDao.updateOne(pool, {
      id: params.id,
      ...body,
    })
    return createHttpResult(200, updatedProduct)
  },
})

export type UpdateProductFetcher = Fetcher<typeof updateProduct>
