export type { Endpoint, Fetcher, Method } from './http-types.js'
export { endpoints } from './endpoints.js'

export type { ActivateUserFetcher } from './routers/activateUser.js'

export type { CreateCompanyFetcher } from './routers/companies/createCompany.js'
export type { DeleteCompanyFetcher } from './routers/companies/deleteCompany.js'
export type { GetCompaniesFetcher } from './routers/companies/getCompanies.js'
export type { GetCompanyFetcher } from './routers/companies/getCompany.js'
export type { UpdateCompanyFetcher } from './routers/companies/updateCompany.js'

export type { CreateCustomerFetcher } from './routers/customers/createCustomer.js'
export type { DeleteCustomerFetcher } from './routers/customers/deleteCustomer.js'
export type { GetCustomerFetcher } from './routers/customers/getCustomer.js'
export type { GetCustomersFetcher } from './routers/customers/getCustomers.js'
export type { UpdateCustomerFetcher } from './routers/customers/updateCustomer.js'

export type { GetOrdersFetcher } from './routers/orders/getOrders.js'
export type { GetOrderFetcher } from './routers/orders/getOrder.js'
export type { CreateOrderFetcher } from './routers/orders/createOrder.js'
export type { UpdateOrderFetcher } from './routers/orders/updateOrder.js'
export type { DeleteOrderFetcher } from './routers/orders/deleteOrder.js'

export type { CreateProductFetcher } from './routers/products/createProduct.js'
export type { GetProductFetcher } from './routers/products/getProduct.js'
export type { GetProductsFetcher } from './routers/products/getProducts.js'
export type { UpdateProductFetcher } from './routers/products/updateProduct.js'

export type { CreateTokenFetcher } from './routers/tokens/createToken.js'
export type { DeleteTokenFetcher } from './routers/tokens/deleteToken.js'
export type { GetTokenFetcher } from './routers/tokens/getToken.js'

export type { AuthorizeUserFetcher } from './routers/users/authorizeUser.js'
export type { GetAuthorizedUsersFetcher } from './routers/users/getAuthorizedUsers.js'
export type { RegisterUserFetcher } from './routers/users/registerUser.js'

export type { CreateUnitFetcher } from './routers/units/createUnit.js'
export type { GetUnitFetcher } from './routers/units/getUnit.js'
export type { GetUnitsFetcher } from './routers/units/getUnits.js'
export type { UpdateUnitFetcher } from './routers/units/updateUnit.js'
