import { sql } from '@ts-safeql/sql-tag'
import { type QueryClient } from '../pool.js'
import { getFirstRow, getRows } from '../utils/index.js'

export const selectManyByCompanyId = (
  client: QueryClient,
  values: { company_id: string },
) =>
  getRows(
    client.query<{
      id: string
      created_at: string
      company_id: string
      name: string
      base_unit_id: string
      base_unit_name: string
    }>(
      sql`select p.*, bu.name as base_unit_name from products p join units bu on bu.id = p.base_unit_id where p.company_id = ${values.company_id}::uuid`,
    ),
  )

export const selectOneById = (client: QueryClient, values: { id: string }) =>
  getFirstRow(
    client.query<{
      id: string
      created_at: string
      company_id: string
      name: string
      base_unit_id: string
      base_unit_name: string
    }>(
      sql`select p.*, bu.name as base_unit_name from products p join units bu on p.base_unit_id = bu.id where p.id = ${values.id}::uuid`,
    ),
  )

export const insertOne = (
  client: QueryClient,
  values: { company_id: string; name: string; base_unit_id: string },
) =>
  getFirstRow(
    client.query<{
      id: string
      created_at: string
      company_id: string
      name: string
      base_unit_id: string
    }>(
      sql`insert into products (company_id, name, base_unit_id) values (${values.company_id}::uuid, ${values.name}, ${values.base_unit_id}::uuid) returning *`,
    ),
  )

export const updateOne = (
  client: QueryClient,
  values: { id: string; name: string },
) =>
  getFirstRow(
    client.query<{
      id: string
      created_at: string
      company_id: string
      name: string
      base_unit_id: string
    }>(
      sql`update products set name = ${values.name} where id = ${values.id}::uuid returning *`,
    ),
  )
